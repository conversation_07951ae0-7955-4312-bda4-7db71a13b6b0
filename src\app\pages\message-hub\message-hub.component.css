/* Mobile-specific styles for popup */
@media (max-width: 767.98px) {
    .mobile-cpt-modal {
        max-width: 95% !important;
        margin: 0 auto;
    }

    .mobile-cpt-modal .modal-content {
        border-radius: 8px;
        overflow: hidden;
    }

    .mobile-cpt-modal .modal-header {
        padding: 0.5rem 1rem;
        background-color: #336699;
        color: white;
    }

    .mobile-cpt-modal .modal-title {
        font-size: 1rem;
        font-weight: 600;
    }

    .mobile-cpt-modal .modal-body {
        padding: 0.75rem;
        max-height: 70vh;
        overflow-y: auto;
    }

    .mobile-cpt-modal .quantity {
        display: inline-flex;
        margin-bottom: 0.5rem;
    }

    .mobile-cpt-modal .quantity__input {
        width: 30px;
        text-align: center;
    }

    .mobile-cpt-modal .form-control {
        font-size: 14px;
    }

    .mobile-cpt-modal .text-danger {
        text-align: center;
        margin-top: 0.5rem;
        font-size: 14px;
    }

    .mobile-cpt-modal .modal-footer {
        padding: 0.5rem;
        justify-content: center;
    }

    .mobile-cpt-modal .btn {
        min-width: 100px;
    }
}

/* iPhone-specific modal fixes for Message Hub */
@media only screen and (max-width: 428px) and (-webkit-min-device-pixel-ratio: 2) {
    /* iPhone-specific modal backdrop fixes */
    .modal-backdrop {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        z-index: 1040 !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
    }

    /* iPhone-specific modal positioning */
    .mobile-cpt-modal {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        z-index: 1050 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .mobile-cpt-modal .modal-dialog {
        position: relative !important;
        margin: 0.5rem !important;
        max-width: 95% !important;
        width: 95% !important;
        transform: none !important;
    }

    .mobile-cpt-modal .modal-content {
        border-radius: 8px !important;
        max-height: 90vh !important;
        overflow: hidden !important;
    }

    /* iPhone-specific close button fixes */
    .mobile-cpt-modal .close {
        position: relative !important;
        z-index: 1051 !important;
        padding: 0.5rem !important;
        margin: 0 !important;
        background: none !important;
        border: none !important;
        font-size: 1.5rem !important;
        line-height: 1 !important;
        color: #fff !important;
        opacity: 1 !important;
        cursor: pointer !important;
        -webkit-appearance: none !important;
        -webkit-tap-highlight-color: transparent !important;
        touch-action: manipulation !important;
    }

    .mobile-cpt-modal .close:hover,
    .mobile-cpt-modal .close:focus,
    .mobile-cpt-modal .close:active {
        color: #fff !important;
        opacity: 0.8 !important;
        text-decoration: none !important;
        outline: none !important;
    }

    /* Ensure proper touch interaction */
    .mobile-cpt-modal .close span {
        pointer-events: none !important;
    }

    /* Fix for modal body scrolling on iPhone */
    .mobile-cpt-modal .modal-body {
        -webkit-overflow-scrolling: touch !important;
        overflow-y: auto !important;
        max-height: 70vh !important;
    }

    /* Prevent body scroll when modal is open */
    body.modal-open {
        position: fixed !important;
        width: 100% !important;
        overflow: hidden !important;
    }
}