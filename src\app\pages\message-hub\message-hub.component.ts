import { Component, OnInit } from '@angular/core';
import { MessageHubService } from 'src/app/services/message-hub/message-hub.service';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
declare let $: any;

@Component({
  selector: 'app-message-hub',
  templateUrl: './message-hub.component.html',
  styleUrls: ['./message-hub.component.css']
})
export class MessageHubComponent implements OnInit {
  public listOfMessages: Array<any> = [];
  public listOfFacilities: Array<any> = [];
  public p: number;
  public searchByName: string;
  public request: any = {};
  public device: boolean = false;
  public navigator = navigator;
  constructor(private readonly mgsServ: MessageHubService, private readonly appComp: AppComponent, private readonly commonServ: CommonService
    , private readonly encrDecr: EncrDecrServiceService, private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.device=this.appComp.device;
    this.appComp.loadPageName('Message Hub', 'messageHubTab');
    this.getMessages();
  }

  isIPhone(): boolean {
    return /iPhone|iPad|iPod/i.test(navigator.userAgent);
  }

  getMessages() {
    this.commonServ.startLoading();
    this.mgsServ.getMessages().subscribe((p: any) => {
      this.listOfMessages = p;
      this.commonServ.stopLoading();
    }, error => { this.commonServ.stopLoading(); });
  }

  markAllAsRead() {
    this.commonServ.startLoading();
    this.request.sGROUP_ID = this.encrDecr.set('0');
    this.request.FLAG = this.encrDecr.set('ALL');
    this.mgsServ.markAsAllRead(this.request).subscribe((p: any) => {
      this.request = {};
      this.mgsServ.getMessages().subscribe((p: any) => {
        this.listOfMessages = p;
      }, error => { });
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
    });
  }

  markAsRead(groupId, isRead) {
    if (!isRead) {
      this.request.sGROUP_ID = this.encrDecr.set(groupId);
      this.request.FLAG = this.encrDecr.set('BADGE');
      this.mgsServ.markAsAllRead(this.request).subscribe((p: any) => {
        this.request = {};
      }, error => {
        this.request = {};
      });
    }

    // iPhone-specific modal initialization
    if (this.isIPhone()) {
      // Clean up any existing modal states before opening new one
      $('.modal-backdrop').remove();
      $('body').removeClass('modal-open');
      $('body').css('overflow', '');
      $('body').css('padding-right', '');

      // Small delay to ensure cleanup is complete before opening modal
      setTimeout(() => {
        const modalId = "#readMsg-" + groupId;
        $(modalId).modal('show');
      }, 50);
    }
  }

  archiveMessage(groupId) {
    this.commonServ.startLoading();
    this.request.sGROUPID = this.encrDecr.set(groupId.toString());
    this.mgsServ.archiveMessage(this.request).subscribe((p: any) => {
      this.request = {};
      if (p > 0) {
        this.mgsServ.getMessages().subscribe((p: any) => {
          this.listOfMessages = p;
        }, error => { });
        this.commonServ.stopLoading();
      }
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
    });
  }

  closeReadMgs(id, isRead) {
    const modalId = "#readMsg-" + id;

    // iPhone-specific modal cleanup
    if (this.isIPhone()) {
      // Remove any stuck modal backdrops
      $('.modal-backdrop').remove();

      // Reset body classes that might be stuck
      $('body').removeClass('modal-open');
      $('body').css('overflow', '');
      $('body').css('padding-right', '');

      // Force hide the modal with additional cleanup
      $(modalId).modal('hide');
      $(modalId).removeClass('show');
      $(modalId).attr('aria-hidden', 'true');

      // Additional cleanup after a short delay to ensure proper state
      setTimeout(() => {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $(modalId).hide();
      }, 100);
    } else {
      // Standard modal hide for non-iPhone devices
      $(modalId).modal('hide');
    }

    if (!isRead) {
      this.mgsServ.getMessages().subscribe((p: any) => {
        this.listOfMessages = p;
      }, error => { });
    }
  }

  openNotes() {
    this.commonServ.startLoading();
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
      $('#sendMessagePop').modal('show');
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); });
  }

}
